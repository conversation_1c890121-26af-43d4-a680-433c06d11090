// Cloudflare Worker to fetch Bible passages from BibleGateway.com
// Optimized for Kindle Paperwhite 7th gen (WebKit 534, ES5 only)

// Display Configuration - Set to true/false to show/hide elements
var showVerseNumbers = false;      // <sup class="versenum">
var showCrossReferences = false;   // <sup class="crossreference">
var showFootnotes = false;         // <sup class="footnote">
var showSectionHeadings = false;   // <h3> section titles

// Generate CSS rules based on display configuration
function generateDisplayCSS() {
    var css = '';

    // Hide elements based on configuration
    if (!showVerseNumbers) {
        css += '        .versenum { display: none; }\n';
    }
    if (!showCrossReferences) {
        css += '        .crossreference { display: none; }\n';
    }
    if (!showFootnotes) {
        css += '        .footnote { display: none; }\n';
    }
    if (!showSectionHeadings) {
        css += '        h3 { display: none; }\n';
    }

    // Always hide chapter numbers and override Words of Jesus styling
    css += '        .chapternum { display: none; }\n';
    css += '        .woj { color: inherit; font-weight: inherit; }\n';

    return css;
}

export default {
    async fetch(request) {
        try {
            // Parse the request URL to get search and version parameters
            var url = new URL(request.url);
            var searchParam = url.searchParams.get('search');
            var versionParam = url.searchParams.get('version');

            if (!searchParam || !versionParam) {
                return new Response(
                    'Invalid URL format. Use: /?search=John%205&version=NIV',
                    { status: 400, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            var translation = versionParam.toUpperCase();
            var passageRef = decodeURIComponent(searchParam);

            // Construct BibleGateway URL using the same parameters
            var bibleGatewayUrl = 'https://www.biblegateway.com/passage/?search=' +
                encodeURIComponent(passageRef) + '&version=' + encodeURIComponent(translation);

            // Fetch the page from BibleGateway
            var response = await fetch(bibleGatewayUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (X11; U; Linux armv7l like Android; en-us) AppleWebKit/531.2 (KHTML, like Gecko) Version/5.0 Safari/533.2 Kindle/3.0'
                }
            });

            if (!response.ok) {
                return new Response(
                    'Failed to fetch passage: ' + response.status + ' ' + response.statusText,
                    { status: 500, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            // Get the raw HTML first to extract div.std-text content
            var htmlText = await response.text();

            // Extract the div.std-text content using a more robust approach
            // Find the opening div.std-text tag
            var stdTextStart = htmlText.search(/<div[^>]*class=['"]std-text['"][^>]*>/);
            if (stdTextStart === -1) {
                return new Response(
                    'Could not find passage content for "' + passageRef + '" in ' + translation +
                    '. The passage may not exist or BibleGateway structure changed.',
                    { status: 404, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            // Find the opening tag match to get its full text
            var openTagMatch = htmlText.substring(stdTextStart).match(/^<div[^>]*class=['"]std-text['"][^>]*>/);
            if (!openTagMatch) {
                return new Response(
                    'Could not parse std-text div tag for "' + passageRef + '" in ' + translation,
                    { status: 404, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            // Start after the opening tag
            var contentStart = stdTextStart + openTagMatch[0].length;
            var divCount = 1; // We've seen one opening div
            var pos = contentStart;

            // Find the matching closing div by counting nested divs
            while (pos < htmlText.length && divCount > 0) {
                var nextDiv = htmlText.indexOf('<div', pos);
                var nextCloseDiv = htmlText.indexOf('</div>', pos);

                if (nextCloseDiv === -1) {
                    // No more closing divs found
                    break;
                }

                if (nextDiv !== -1 && nextDiv < nextCloseDiv) {
                    // Found an opening div before the next closing div
                    divCount++;
                    pos = nextDiv + 4; // Move past '<div'
                } else {
                    // Found a closing div
                    divCount--;
                    if (divCount === 0) {
                        // This is our matching closing div
                        var contentEnd = nextCloseDiv;
                        var stdTextContent = htmlText.substring(stdTextStart, contentEnd + 6); // Include </div>
                        break;
                    }
                    pos = nextCloseDiv + 6; // Move past '</div>'
                }
            }

            if (divCount > 0) {
                return new Response(
                    'Could not find complete std-text content for "' + passageRef + '" in ' + translation +
                    '. The HTML structure may be malformed.',
                    { status: 404, headers: { 'Content-Type': 'text/plain' } }
                );
            }

            // Extract title from h1.bcv (handle both single and double quotes)
            var titleMatch = htmlText.match(/<h1[^>]*class=['"][^'"]*bcv[^'"]*['"][^>]*>([\s\S]*?)<\/h1>/);
            var title = '';
            if (titleMatch) {
                // Remove HTML tags from title
                title = titleMatch[1].replace(/<[^>]*>/g, '').trim();
            }

            // Clean up title - remove translation suffix
            var cleanTitle = title || passageRef;
            if (cleanTitle.indexOf(' - ') !== -1) {
                cleanTitle = cleanTitle.substring(0, cleanTitle.indexOf(' - '));
            }

            // Generate Kindle-optimized HTML
            var html = '<!DOCTYPE html>\n' +
                '<html lang="en">\n' +
                '<head>\n' +
                '    <meta charset="UTF-8">\n' +
                '    <title>' + cleanTitle + '</title>\n' +
                '    <style>\n' +
                '        /* Kindle Paperwhite 7th gen optimizations */\n' +
                '        body {\n' +
                '            width: 100%; /* Required for clientWidth to work */\n' +
                '            font-family: serif;\n' +
                '            font-size: 1.8rem; /* Large text for e-ink readability */\n' +
                '            line-height: 1.7;\n' +
                '            margin: 0;\n' +
                '            padding: 2rem;\n' +
                '            background-color: white;\n' +
                '            color: black;\n' +
                '            /* Hide scrollbar using nested container trick */\n' +
                '            overflow: hidden;\n' +
                '        }\n' +
                '        .content-wrapper {\n' +
                '            width: 103%;\n' +
                '            height: 100vh;\n' +
                '            overflow-y: auto;\n' +
                '            padding-right: 3%;\n' +
                '            margin-right: -3%;\n' +
                '        }\n' +
                '        .inner-content {\n' +
                '            max-width: 1000px;\n' +
                '            margin: 0 auto;\n' +
                '        }\n' +
                '        h1 {\n' +
                '            font-size: 2.2rem;\n' +
                '            text-align: left;\n' +
                '            margin-bottom: 2rem;\n' +
                '            font-weight: normal;\n' +
                '        }\n' +
                '        /* Preserve BibleGateway styling but optimize for Kindle */\n' +
                '        .std-text {\n' +
                '            font-size: inherit;\n' +
                '            line-height: inherit;\n' +
                '        }\n' +
                '        .std-text p {\n' +
                '            margin-bottom: 1.2rem;\n' +
                '        }\n' +
                '        /* Style verse numbers if present */\n' +
                '        .versenum {\n' +
                '            font-size: 0.8rem;\n' +
                '            vertical-align: super;\n' +
                '            color: #666;\n' +
                '        }\n' +
                '        /* Style cross-references and footnotes if present */\n' +
                '        .crossreference, .footnote {\n' +
                '            font-size: 0.7rem;\n' +
                '            vertical-align: super;\n' +
                '            color: #999;\n' +
                '        }\n' +
                '        /* Remove link underlines using data-href workaround */\n' +
                '        a {\n' +
                '            color: inherit;\n' +
                '            text-decoration: none;\n' +
                '        }\n' +
                '        /* Float-based layout for compatibility */\n' +
                '        .float-left { float: left; }\n' +
                '        .float-right { float: right; }\n' +
                '        .clearfix:after {\n' +
                '            content: "";\n' +
                '            display: table;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '        .footer {\n' +
                '            margin-top: 3rem;\n' +
                '            text-align: center;\n' +
                '            font-size: 1rem;\n' +
                '            color: #666;\n' +
                '            clear: both;\n' +
                '        }\n' +
                '        /* Dynamic display configuration */\n' +
                generateDisplayCSS() +
                '    </style>\n' +
                '</head>\n' +
                '<body>\n' +
                '    <div class="content-wrapper">\n' +
                '        <div class="inner-content">\n' +
                '            <h1>' + cleanTitle + '</h1>\n' +
                '            ' + stdTextContent + '\n' +
                '            <div class="footer">\n' +
                '                <p>' + translation.toUpperCase() + ' - BibleGateway.com</p>\n' +
                '            </div>\n' +
                '        </div>\n' +
                '    </div>\n' +
                '</body>\n' +
                '</html>';

            return new Response(html, {
                headers: { 'Content-Type': 'text/html; charset=utf-8' }
            });

        } catch (error) {
            return new Response(
                'Internal server error: ' + error.message,
                { status: 500, headers: { 'Content-Type': 'text/plain' } }
            );
        }
    }
};