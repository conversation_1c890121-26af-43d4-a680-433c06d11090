# Project: bible-poc

## Project Overview

This project is a Cloudflare Worker designed to fetch Bible passages from Bible Gateway, cleanse the HTML, and present it in a clean, readable format. It's a single-endpoint service that takes a Bible reference (translation, book, and chapter) as input via the URL and returns a minimalist HTML page with the requested passage.

The core logic is built using Cloudflare Workers and the `HTMLRewriter` API to efficiently stream and transform the HTML content. It removes unwanted elements like footnotes, verse numbers, and cross-references, and then reconstructs the passage text.

**Key Technologies:**

*   **Runtime:** Cloudflare Workers
*   **Language:** JavaScript
*   **Testing:** Vitest
*   **Deployment:** Wrangler CLI

## Building and Running

### Prerequisites

*   Node.js and npm
*   Wrangler CLI (`npm install -g wrangler`)

### Development

To run the worker locally for development, use the following command:

```bash
npm run dev
```

This will start a local server, and you can access the worker at `http://localhost:8787`.

### Testing

The project uses Vitest for testing. To run the tests, use:

```bash
npm test
```

**Note:** The current tests in `test/index.spec.js` are placeholder "Hello World" tests and do not reflect the actual functionality of the worker.

### Deployment

To deploy the worker to Cloudflare, use the following command:

```bash
npm run deploy
```

## Development Conventions

### Code Style

The project uses Prettier for code formatting. It's recommended to use an editor extension to automatically format code on save.

### API Usage

The worker expects a URL structure of `/{Translation}/{Book}/{Chapter}`. For example, to get the first chapter of the book of John in the NIV translation, you would use the following URL:

`/NIV/John/1`

If the URL is not in the correct format, the worker will return a 400 Bad Request response with usage instructions.

### Error Handling

The worker includes basic error handling for cases where:

*   The Bible Gateway URL cannot be fetched.
*   The passage content cannot be found in the fetched HTML.
*   An error occurs during the HTML transformation process.

In these cases, the worker will return an appropriate error message and status code.
