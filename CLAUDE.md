# CLAUDE.md

## Project Overview

This is a Cloudflare Workers project that serves as a Bible passage retrieval service. The application scrapes Bible Gateway to fetch and format Bible passages with clean HTML output.

## Development Commands

- `npm run dev` - Start local development server using Wrangler
- `npm run start` - Alternative command to start development server
- `npm run deploy` - Deploy to Cloudflare Workers

## Architecture

The project is built as a Cloudflare Worker with the following structure:

- **src/index.js** - Main worker entry point that handles HTTP requests, scrapes Bible Gateway, and returns formatted HTML
- **wrangler.jsonc** - Cloudflare Workers configuration file
- **test/index.spec.js** - Test file using Vitest and Cloudflare Workers testing utilities

## Key Implementation Details

- Uses Cloudflare Workers HTMLRewriter API to parse and extract content from Bible Gateway
- Accepts URL format: `/{Translation}/{Book}/{Chapter}` (e.g., `/NIV/John/1`)
- Scrapes Bible Gateway using custom HtmlBuilder class to capture passage content
- Returns clean, formatted HTML with custom styling optimized for reading
- Handles error cases when passages cannot be found or extracted
- Uses User-Agent header to ensure successful requests to Bible Gateway
